"""
GAIA Workforce Factory

This module provides factory functions for creating and configuring
GAIA workforce instances based on the exact logic from run_gaia_workforce.py.
"""

from camel.models import ModelFactory
from camel.types import ModelPlatformType, ModelType
from camel.toolkits import (
    VideoAnalysisToolkit,
    SearchToolkit,
    CodeExecutionToolkit,
    ImageAnalysisToolkit,
    DocumentProcessingToolkit,
    AudioAnalysisToolkit,
    AsyncBrowserToolkit,
    ExcelToolkit,
)

from utils.enhanced_workforce import OwlGaiaWorkforce
from loguru import logger





def create_gaia_workforce(llm_config) -> OwlGaiaWorkforce:
    """
    Create a GAIA workforce with custom LLM configuration.

    Args:
        llm_config: LLMConfig object containing model configuration

    Returns:
        OwlGaiaWorkforce configured with the specified LLM settings
    """
    logger.info(f"Creating GAIA workforce with custom LLM config: {llm_config.model_platform}/{llm_config.model_type}")

    # Convert LLMConfig to ModelFactory parameters
    model_platform_map = {
        "openai": ModelPlatformType.OPENAI,
        "anthropic": ModelPlatformType.ANTHROPIC,
        "vllm": ModelPlatformType.VLLM,
        "azure": ModelPlatformType.AZURE,
        "groq": ModelPlatformType.GROQ,
        "lmstudio": ModelPlatformType.LMSTUDIO,
        "openrouter": ModelPlatformType.OPENROUTER,
        "zhipuai": ModelPlatformType.ZHIPUAI,
        "gemini": ModelPlatformType.GEMINI,
        "mistral": ModelPlatformType.MISTRAL,
        "ollama": ModelPlatformType.OLLAMA,
        "sglang": ModelPlatformType.SGLANG,
        "openai_compatible": ModelPlatformType.OPENAI_COMPATIBLE_MODEL,
        "samba": ModelPlatformType.SAMBA,
        "together": ModelPlatformType.TOGETHER,
        "litellm": ModelPlatformType.LITELLM,
        "aws_bedrock": ModelPlatformType.AWS_BEDROCK,
        "nvidia": ModelPlatformType.NVIDIA,
        "siliconflow": ModelPlatformType.SILICONFLOW,
        "aiml": ModelPlatformType.AIML,
        "volcano": ModelPlatformType.VOLCANO,
    }

    model_platform = model_platform_map.get(llm_config.model_platform.lower(), ModelPlatformType.OPENAI)

    # Create model configuration
    model_config_dict = llm_config.model_config_dict or {"temperature": 0}

    # Create the main model for task and answerer agents
    main_model = ModelFactory.create(
        model_platform=model_platform,
        model_type=llm_config.model_type,
        model_config_dict=model_config_dict,
        api_key=llm_config.api_key,
        url=llm_config.url,
        timeout=llm_config.timeout,
    )

    # For coordinator, use the same model or fallback to default if it's not compatible
    coordinator_model = main_model
    try:
        # Test if the model can be used for coordination
        # Some models might not support all features needed for coordination
        coordinator_model = ModelFactory.create(
            model_platform=model_platform,
            model_type=llm_config.model_type,
            model_config_dict=model_config_dict,
            api_key=llm_config.api_key,
            url=llm_config.url,
            timeout=llm_config.timeout,
        )
    except Exception as e:
        logger.warning(f"Failed to create coordinator model with custom config, using default: {e}")
        coordinator_model = ModelFactory.create(
            model_platform=ModelPlatformType.OPENAI,
            model_type=ModelType.GPT_4O,
            model_config_dict={"temperature": 0},
        )

    # Create fixed models that should not use client configuration
    # Fix - Keep these models using environment variables
    image_analysis_model = ModelFactory.create(
        model_platform=ModelPlatformType.OPENAI,
        model_type=ModelType.GPT_4O,
        model_config_dict={"temperature": 0},
    )

    # Fix - Keep these models using environment variables
    audio_reasoning_model = ModelFactory.create(
        model_platform=ModelPlatformType.OPENAI,
        model_type=ModelType.O3_MINI,
        model_config_dict={"temperature": 0},
    )

    # Create workforce with custom models
    coordinator_agent_kwargs = {"model": coordinator_model}
    task_agent_kwargs = {"model": main_model}
    answerer_agent_kwargs = {"model": main_model}

    workforce = OwlGaiaWorkforce(
        "Gaia Workforce",
        task_agent_kwargs=task_agent_kwargs,
        coordinator_agent_kwargs=coordinator_agent_kwargs,
        answerer_agent_kwargs=answerer_agent_kwargs
    )

    # Initialize toolkits (same as in construct_workforce)
    search_toolkit = SearchToolkit()
    document_processing_toolkit = DocumentProcessingToolkit(cache_dir="tmp")

    # For image analysis, use the fixed model (marked as Fix)
    image_analysis_toolkit = ImageAnalysisToolkit(model=image_analysis_model)

    video_analysis_toolkit = VideoAnalysisToolkit(download_directory="tmp/video")

    # For audio analysis, use the fixed model (marked as Fix)
    audio_analysis_toolkit = AudioAnalysisToolkit(cache_dir="tmp/audio", audio_reasoning_model=audio_reasoning_model)

    code_runner_toolkit = CodeExecutionToolkit(sandbox="subprocess", verbose=True)

    # For browser toolkit, use the main model for both planning and web agents
    browser_simulator_toolkit = AsyncBrowserToolkit(
        headless=True,
        cache_dir="tmp/browser",
        planning_agent_model=main_model,
        web_agent_model=main_model
    )

    excel_toolkit = ExcelToolkit()

    # Add toolkits to workforce (same as in construct_workforce)
    workforce.add_single_agent_with_tools(
        "Web Search Agent",
        search_toolkit.get_tools(),
        model=main_model
    )

    workforce.add_single_agent_with_tools(
        "Document Processing Agent",
        document_processing_toolkit.get_tools(),
        model=main_model
    )

    workforce.add_single_agent_with_tools(
        "Image Analysis Agent",
        image_analysis_toolkit.get_tools(),
        model=main_model
    )

    workforce.add_single_agent_with_tools(
        "Video Analysis Agent",
        video_analysis_toolkit.get_tools(),
        model=main_model
    )

    workforce.add_single_agent_with_tools(
        "Audio Analysis Agent",
        audio_analysis_toolkit.get_tools(),
        model=main_model
    )

    workforce.add_single_agent_with_tools(
        "Code Execution Agent",
        code_runner_toolkit.get_tools(),
        model=main_model
    )

    workforce.add_single_agent_with_tools(
        "Browser Simulation Agent",
        browser_simulator_toolkit.get_tools(),
        model=main_model
    )

    workforce.add_single_agent_with_tools(
        "Excel Processing Agent",
        excel_toolkit.get_tools(),
        model=main_model
    )

    logger.info("GAIA workforce with custom LLM config created successfully")
    return workforce
