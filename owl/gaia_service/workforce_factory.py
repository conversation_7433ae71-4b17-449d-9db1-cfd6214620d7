"""
GAIA Workforce Factory

This module provides factory functions for creating and configuring
GAIA workforce instances based on the exact logic from run_gaia_workforce.py.
"""

from camel.models import ModelFactory
from camel.types import ModelPlatformType, ModelType
from camel.toolkits import (
    VideoAnalysisToolkit,
    SearchToolkit,
    CodeExecutionToolkit,
    ImageAnalysisToolkit,
    DocumentProcessingToolkit,
    AudioAnalysisToolkit,
    AsyncBrowserToolkit,
    ExcelToolkit,
    FunctionTool
)

from utils.enhanced_workforce import OwlWorkforceChatAgent, OwlGaiaWorkforce
from loguru import logger



def construct_agent_list(llm_config) -> List[Dict[str, Any]]:
    """
    Construct agent list exactly as in run_gaia_workforce.py, but with client configuration for non-Fix models.
    """
    # Convert LLMConfig to ModelFactory parameters
    model_platform_map = {
        "openai": ModelPlatformType.OPENAI,
        "anthropic": ModelPlatformType.ANTHROPIC,
        "vllm": ModelPlatformType.VLLM,
        "azure": ModelPlatformType.AZURE,
        "groq": ModelPlatformType.GROQ,
        "lmstudio": ModelPlatformType.LMSTUDIO,
        "openrouter": ModelPlatformType.OPENROUTER,
        "zhipuai": ModelPlatformType.ZHIPUAI,
        "gemini": ModelPlatformType.GEMINI,
        "mistral": ModelPlatformType.MISTRAL,
        "ollama": ModelPlatformType.OLLAMA,
        "sglang": ModelPlatformType.SGLANG,
        "openai_compatible": ModelPlatformType.OPENAI_COMPATIBLE_MODEL,
        "samba": ModelPlatformType.SAMBA,
        "together": ModelPlatformType.TOGETHER,
        "litellm": ModelPlatformType.LITELLM,
        "aws_bedrock": ModelPlatformType.AWS_BEDROCK,
        "nvidia": ModelPlatformType.NVIDIA,
        "siliconflow": ModelPlatformType.SILICONFLOW,
        "aiml": ModelPlatformType.AIML,
        "volcano": ModelPlatformType.VOLCANO,
    }

    model_platform = model_platform_map.get(llm_config.model_platform.lower(), ModelPlatformType.OPENAI)
    model_config_dict = llm_config.model_config_dict or {"temperature": 0}

    # Create client-configured models (non-Fix models)
    web_model = ModelFactory.create(
        model_platform=model_platform,
        model_type=llm_config.model_type,
        model_config_dict=model_config_dict,
        api_key=llm_config.api_key,
        url=llm_config.url,
        timeout=llm_config.timeout,
    )

    document_processing_model = ModelFactory.create(
        model_platform=model_platform,
        model_type=llm_config.model_type,
        model_config_dict=model_config_dict,
        api_key=llm_config.api_key,
        url=llm_config.url,
        timeout=llm_config.timeout,
    )

    reasoning_model = ModelFactory.create(
        model_platform=model_platform,
        model_type=llm_config.model_type,
        model_config_dict=model_config_dict,
        api_key=llm_config.api_key,
        url=llm_config.url,
        timeout=llm_config.timeout,
    )

    # Fix - Keep these models using environment variables
    image_analysis_model = ModelFactory.create(
        model_platform=ModelPlatformType.OPENAI,
        model_type=ModelType.GPT_4O,
        model_config_dict={"temperature": 0},
    )

    # Fix - Keep these models using environment variables
    audio_reasoning_model = ModelFactory.create(
        model_platform=ModelPlatformType.OPENAI,
        model_type=ModelType.O3_MINI,
        model_config_dict={"temperature": 0},
    )

    web_agent_model = ModelFactory.create(
        model_platform=model_platform,
        model_type=llm_config.model_type,
        model_config_dict=model_config_dict,
        api_key=llm_config.api_key,
        url=llm_config.url,
        timeout=llm_config.timeout,
    )

    planning_agent_model = ModelFactory.create(
        model_platform=model_platform,
        model_type=llm_config.model_type,
        model_config_dict=model_config_dict,
        api_key=llm_config.api_key,
        url=llm_config.url,
        timeout=llm_config.timeout,
    )

    # Initialize toolkits
    search_toolkit = SearchToolkit()
    document_processing_toolkit = DocumentProcessingToolkit(cache_dir="tmp")
    image_analysis_toolkit = ImageAnalysisToolkit(model=image_analysis_model)
    video_analysis_toolkit = VideoAnalysisToolkit(download_directory="tmp/video")
    audio_analysis_toolkit = AudioAnalysisToolkit(cache_dir="tmp/audio", audio_reasoning_model=audio_reasoning_model)
    code_runner_toolkit = CodeExecutionToolkit(sandbox="subprocess", verbose=True)
    browser_simulator_toolkit = AsyncBrowserToolkit(headless=True, cache_dir="tmp/browser", planning_agent_model=planning_agent_model, web_agent_model=web_agent_model)
    excel_toolkit = ExcelToolkit()

    # Create agents exactly as in run_gaia_workforce.py
    web_agent = OwlWorkforceChatAgent(
"""
You are a helpful assistant that can search the web, extract webpage content, simulate browser actions, and provide relevant information to solve the given task.
Keep in mind that:
- Do not be overly confident in your own knowledge. Searching can provide a broader perspective and help validate existing knowledge.
- If one way fails to provide an answer, try other ways or methods. The answer does exists.
- If the search snippet is unhelpful but the URL comes from an authoritative source, try visit the website for more details.
- When looking for specific numerical values (e.g., dollar amounts), prioritize reliable sources and avoid relying only on search snippets.
- When solving tasks that require web searches, check Wikipedia first before exploring other websites.
- You can also simulate browser actions to get more information or verify the information you have found.
- Browser simulation is also helpful for finding target URLs. Browser simulation operations do not necessarily need to find specific answers, but can also help find web page URLs that contain answers (usually difficult to find through simple web searches). You can find the answer to the question by performing subsequent operations on the URL, such as extracting the content of the webpage.
- Do not solely rely on document tools or browser simulation to find the answer, you should combine document tools and browser simulation to comprehensively process web page information. Some content may need to do browser simulation to get, or some content is rendered by javascript.
- In your response, you should mention the urls you have visited and processed.

Here are some tips that help you perform web search:
- Never add too many keywords in your search query! Some detailed results need to perform browser interaction to get, not using search toolkit.
- If the question is complex, search results typically do not provide precise answers. It is not likely to find the answer directly using search toolkit only, the search query should be concise and focuses on finding official sources rather than direct answers.
  For example, as for the question "What is the maximum length in meters of #9 in the first National Geographic short on YouTube that was ever released according to the Monterey Bay Aquarium website?", your first search term must be coarse-grained like "National Geographic YouTube" to find the youtube website first, and then try other fine-grained search terms step-by-step to find more urls.
- The results you return do not have to directly answer the original question, you only need to collect relevant information.
""",
        model=web_model,
        tools=[
            FunctionTool(search_toolkit.search_google_serpapi),
            FunctionTool(search_toolkit.search_wiki),
            FunctionTool(search_toolkit.search_wiki_revisions),
            FunctionTool(search_toolkit.search_archived_webpage),
            FunctionTool(document_processing_toolkit.extract_document_content),
            FunctionTool(browser_simulator_toolkit.browse_url),
            FunctionTool(video_analysis_toolkit.ask_question_about_video),
        ]
    )

    document_processing_agent = OwlWorkforceChatAgent(
        "You are a helpful assistant that can process documents and multimodal data, such as images, audio, and video.",
        document_processing_model,
        tools=[
            FunctionTool(document_processing_toolkit.extract_document_content),
            FunctionTool(image_analysis_toolkit.ask_question_about_image),
            FunctionTool(audio_analysis_toolkit.ask_question_about_audio),
            FunctionTool(video_analysis_toolkit.ask_question_about_video),
            FunctionTool(code_runner_toolkit.execute_code),
        ]
    )

    reasoning_coding_agent = OwlWorkforceChatAgent(
        "You are a helpful assistant that specializes in reasoning and coding, and can think step by step to solve the task. When necessary, you can write python code to solve the task. If you have written code, do not forget to execute the code. Never generate codes like 'example code', your code should be able to fully solve the task. You can also leverage multiple libraries, such as requests, BeautifulSoup, re, pandas, etc, to solve the task. For processing excel files, you should write codes to process them.",
        reasoning_model,
        tools=[
            FunctionTool(code_runner_toolkit.execute_code),
            FunctionTool(excel_toolkit.extract_excel_content),
            FunctionTool(document_processing_toolkit.extract_document_content),
        ]
    )

    agent_list = []

    web_agent_dict = {
        "name": "Web Agent",
        "description": "A helpful assistant that can search the web, extract webpage content, simulate browser actions, and retrieve relevant information.",
        "agent": web_agent
    }

    document_processing_agent_dict = {
        "name": "Document Processing Agent",
        "description": "A helpful assistant that can process a variety of local and remote documents, including pdf, docx, images, audio, and video, etc.",
        "agent": document_processing_agent
    }

    reasoning_coding_agent_dict = {
        "name": "Reasoning Coding Agent",
        "description": "A helpful assistant that specializes in reasoning, coding, and processing excel files. However, it cannot access the internet to search for information. If the task requires python execution, it should be informed to execute the code after writing it.",
        "agent": reasoning_coding_agent
    }

    agent_list.append(web_agent_dict)
    agent_list.append(document_processing_agent_dict)
    agent_list.append(reasoning_coding_agent_dict)
    return agent_list


def create_gaia_workforce(llm_config) -> OwlGaiaWorkforce:
    """
    Create a GAIA workforce with custom LLM configuration.
    Follows the exact structure of run_gaia_workforce.py.
    Only non-Fix models use client configuration, Fix models use environment variables.

    Args:
        llm_config: LLMConfig object containing model configuration for non-Fix models

    Returns:
        OwlGaiaWorkforce configured with the specified LLM settings
    """
    logger.info(f"Creating GAIA workforce with custom LLM config: {llm_config.model_platform}/{llm_config.model_type}")

    # Convert LLMConfig to ModelFactory parameters for coordinator, task, and answerer models
    model_platform_map = {
        "openai": ModelPlatformType.OPENAI,
        "anthropic": ModelPlatformType.ANTHROPIC,
        "vllm": ModelPlatformType.VLLM,
        "azure": ModelPlatformType.AZURE,
        "groq": ModelPlatformType.GROQ,
        "lmstudio": ModelPlatformType.LMSTUDIO,
        "openrouter": ModelPlatformType.OPENROUTER,
        "zhipuai": ModelPlatformType.ZHIPUAI,
        "gemini": ModelPlatformType.GEMINI,
        "mistral": ModelPlatformType.MISTRAL,
        "ollama": ModelPlatformType.OLLAMA,
        "sglang": ModelPlatformType.SGLANG,
        "openai_compatible": ModelPlatformType.OPENAI_COMPATIBLE_MODEL,
        "samba": ModelPlatformType.SAMBA,
        "together": ModelPlatformType.TOGETHER,
        "litellm": ModelPlatformType.LITELLM,
        "aws_bedrock": ModelPlatformType.AWS_BEDROCK,
        "nvidia": ModelPlatformType.NVIDIA,
        "siliconflow": ModelPlatformType.SILICONFLOW,
        "aiml": ModelPlatformType.AIML,
        "volcano": ModelPlatformType.VOLCANO,
    }

    model_platform = model_platform_map.get(llm_config.model_platform.lower(), ModelPlatformType.OPENAI)
    model_config_dict = llm_config.model_config_dict or {"temperature": 0}

    # Create workforce exactly as in run_gaia_workforce.py
    coordinator_agent_kwargs = {
        "model": ModelFactory.create(
            model_platform=model_platform,
            model_type=llm_config.model_type,
            model_config_dict=model_config_dict,
            api_key=llm_config.api_key,
            url=llm_config.url,
            timeout=llm_config.timeout,
        )
    }

    task_agent_kwargs = {
        "model": ModelFactory.create(
            model_platform=model_platform,
            model_type=llm_config.model_type,
            model_config_dict=model_config_dict,
            api_key=llm_config.api_key,
            url=llm_config.url,
            timeout=llm_config.timeout,
        )
    }

    answerer_agent_kwargs = {
        "model": ModelFactory.create(
            model_platform=model_platform,
            model_type=llm_config.model_type,
            model_config_dict=model_config_dict,
            api_key=llm_config.api_key,
            url=llm_config.url,
            timeout=llm_config.timeout,
        )
    }

    workforce = OwlGaiaWorkforce(
        "Gaia Workforce",
        task_agent_kwargs=task_agent_kwargs,
        coordinator_agent_kwargs=coordinator_agent_kwargs,
        answerer_agent_kwargs=answerer_agent_kwargs
    )

    # Get agent list exactly as in run_gaia_workforce.py
    agent_list = construct_agent_list(llm_config)

    # Add agents to workforce exactly as in run_gaia_workforce.py
    for agent_dict in agent_list:
        workforce.add_single_agent_worker(
            agent_dict["description"],
            worker=agent_dict["agent"],
        )

    logger.info("GAIA workforce with custom LLM config created successfully")
    return workforce
