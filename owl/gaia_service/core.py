"""
GAIA Evaluation Service Core

This module provides a simplified evaluation service that directly wraps
the existing GAIA workforce evaluation logic from run_gaia_workforce.py.
"""

import asyncio
import os
from datetime import datetime
from typing import Dict, Any, Optional, Union
from uuid import uuid4

from loguru import logger

from camel.tasks import Task
from utils.enhanced_workforce import OwlGaiaWorkforce
from utils.gaia import GAIABenchmark

from .models import (
    EvaluationRequest,
    EvaluationResponse,
    EvaluationResult,
    ExecutionTrace,
    GAIAQuery,
    TaskStatus
)


class GAIAEvaluationService:
    """
    Simplified GAIA evaluation service that wraps the existing workforce logic.
    
    This service provides a thin wrapper around the core GAIA evaluation
    logic from run_gaia_workforce.py, making it available as a web service.
    """
    
    def __init__(self, workforce: Optional[OwlGaiaWorkforce] = None, temp_dir: str = "tmp"):
        """
        Initialize the GAIA evaluation service.

        Args:
            workforce: Optional default OwlGaiaWorkforce instance. If None, workforce will be created per request.
            temp_dir: Directory for temporary files during processing
        """
        self.workforce = workforce
        self.temp_dir = temp_dir
        self.active_tasks: Dict[str, EvaluationResponse] = {}
        self.completed_tasks: Dict[str, EvaluationResponse] = {}
        self.task_futures: Dict[str, asyncio.Task] = {}
        self.start_time = datetime.utcnow()
        
        # Ensure temp directory exists
        os.makedirs(self.temp_dir, exist_ok=True)
        
        # Initialize benchmark for scoring
        self.benchmark = GAIABenchmark(
            data_dir="data/gaia",
            save_to=os.path.join(self.temp_dir, "service_results.json")
        )

    async def submit_evaluation(self, request: EvaluationRequest) -> str:
        """Submit a GAIA query for evaluation."""
        task_id = str(uuid4())
        
        # Create initial response object
        response = EvaluationResponse(
            task_id=task_id,
            status=TaskStatus.PENDING,
            created_at=datetime.utcnow()
        )
        
        # Store in active tasks
        self.active_tasks[task_id] = response
        
        # Start asynchronous evaluation
        future = asyncio.create_task(self._process_evaluation(task_id, request))
        self.task_futures[task_id] = future
        
        logger.info(f"Submitted evaluation task {task_id} for question: {request.query.question[:100]}...")
        
        return task_id

    async def get_task_status(self, task_id: str) -> Optional[EvaluationResponse]:
        """Get the current status of a task."""
        if task_id in self.active_tasks:
            return self.active_tasks[task_id]
        elif task_id in self.completed_tasks:
            return self.completed_tasks[task_id]
        return None

    async def get_evaluation_result(self, task_id: str) -> Optional[EvaluationResponse]:
        """Get the complete evaluation result."""
        if task_id in self.completed_tasks:
            return self.completed_tasks[task_id]
        return None

    def get_service_stats(self) -> Dict[str, Any]:
        """Get service statistics and health information."""
        uptime = (datetime.utcnow() - self.start_time).total_seconds()
        
        return {
            "uptime_seconds": int(uptime),
            "active_tasks": len(self.active_tasks),
            "completed_tasks": len(self.completed_tasks),
            "failed_tasks": len([t for t in self.completed_tasks.values() if t.status == TaskStatus.FAILED]),
            "workforce_running": self.workforce.is_running() if hasattr(self.workforce, 'is_running') else False
        }

    async def _process_evaluation(self, task_id: str, request: EvaluationRequest):
        """Process a single GAIA evaluation task using the simplified workflow."""
        response = self.active_tasks[task_id]
        
        try:
            # Update status to processing
            response.status = TaskStatus.PROCESSING
            
            # Create execution trace
            trace = ExecutionTrace(
                task_id=task_id,
                start_time=datetime.utcnow()
            )
            response.execution_trace = trace
            
            # Process the task using the simplified workflow (like run_gaia_workforce.py)
            result = await self._run_single_task(request.query, request.max_replanning_tries, trace, request.llm_config)
            
            # Complete the evaluation
            trace.end_time = datetime.utcnow()
            trace.total_duration_ms = int(
                (trace.end_time - trace.start_time).total_seconds() * 1000
            )
            
            response.evaluation_result = result
            response.status = TaskStatus.COMPLETED
            response.completed_at = datetime.utcnow()
            
            logger.success(f"Task {task_id} completed successfully")
                
        except Exception as e:
            logger.error(f"Error processing task {task_id}: {str(e)}")
            
            response.status = TaskStatus.FAILED
            response.error_message = str(e)
            response.completed_at = datetime.utcnow()
            
            if response.execution_trace:
                response.execution_trace.error_messages.append(str(e))
        
        finally:
            # Move from active to completed
            if task_id in self.active_tasks:
                self.completed_tasks[task_id] = self.active_tasks.pop(task_id)
            
            # Clean up future
            if task_id in self.task_futures:
                del self.task_futures[task_id]

    async def _run_single_task(self, query: GAIAQuery, max_replanning_tries: int, trace: ExecutionTrace, llm_config=None) -> EvaluationResult:
        """
        Run a single GAIA task using the simplified workflow from run_gaia_workforce.py.
        
        This method follows the same logic as GAIABenchmark.run_workforce_with_retry
        but for a single task.
        """
        try:
            # Create CAMEL task (similar to benchmark._create_task)
            camel_task = Task(
                content=query.question,
                additional_info=query.additional_info,
                id=trace.task_id
            )
            camel_task.overall_task = query.question

            # Add file information if available
            if query.file_attachments:
                file_info = f"\n\nFile attachments: {', '.join(query.file_attachments)}"
                camel_task.additional_info = (camel_task.additional_info + file_info) if camel_task.additional_info else file_info.strip()

            # Create workforce based on LLM config or use default
            workforce = self._get_workforce_for_config(llm_config)

            # Stop workforce if running (like in the benchmark)
            if workforce.is_running():
                workforce.stop()

            # Process task with workforce (core logic from run_workforce_with_retry)
            loop = asyncio.get_event_loop()
            processed_task = await loop.run_in_executor(
                None,
                lambda: workforce.process_task(camel_task, max_replanning_tries=max_replanning_tries)
            )
            
            # Get the final answer (like in the benchmark)
            try:
                answer = workforce.get_workforce_final_answer(processed_task)
            except Exception as e:
                logger.error(f"Error extracting final answer: {e}")
                answer = None
            
            # Score the answer (like in the benchmark)
            score = self.benchmark.question_scorer(answer, query.final_answer)
            is_correct = score == True
            
            # Capture trajectory
            trajectory = []
            try:
                workforce_trajectory = self.workforce.get_overall_task_solve_trajectory()
                if workforce_trajectory:
                    trace.workforce_trajectory = workforce_trajectory
                    # Convert to simple format for the trace
                    for i, traj in enumerate(workforce_trajectory):
                        trajectory.append({
                            "step": i + 1,
                            "content": str(traj)
                        })
            except Exception as e:
                logger.warning(f"Could not capture trajectory: {e}")
            
            logger.info(f"Model answer: {answer}, Ground truth: {query.final_answer}, Score: {score}")
            
            return EvaluationResult(
                correct=is_correct,
                model_answer=answer or "",
                ground_truth=query.final_answer,
                score=1.0 if is_correct else 0.0
            )
            
        except Exception as e:
            logger.error(f"Error in task processing: {e}")
            return EvaluationResult(
                correct=False,
                model_answer="",
                ground_truth=query.final_answer,
                score=0.0
            )

    def _get_workforce_for_config(self, llm_config):
        """Get workforce based on LLM configuration."""
        if llm_config is None:
            raise ValueError("LLM configuration is required. No default workforce available.")

        # Import workforce factory
        from .workforce_factory import create_gaia_workforce

        # Create workforce with custom LLM config
        return create_gaia_workforce(llm_config)
